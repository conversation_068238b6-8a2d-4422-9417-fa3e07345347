<template>
  <!-- 左侧科室列表 -->
  <div class="dept-list-card">
    <div class="dept-header">
      <n-icon size="18" class="dept-icon"><BuildOutline /></n-icon>
      <span>医院科室绩效</span>
    </div>
    <n-input v-model:value="deptSearchPattern" placeholder="搜索科室" class="dept-search">
      <template #prefix>
        <n-icon><SearchOutline /></n-icon>
      </template>
    </n-input>
    <n-scrollbar :style="{ height: treeHeight }" class="dept-scrollbar">
      <n-list hoverable clickable>
        <n-list-item
          v-for="(item, index) in filteredTemplateList"
          :key="item.id"
          :class="{ 'active-dept': selectedDeptTemplate?.id === item.id }"
          @click="handleDeptClick(item)"
        >
          <div class="dept-item">
            <div class="dept-name">
              <!-- <n-icon class="dept-type-icon" size="14"><FolderOutline /></n-icon> -->
              <span class="dept-name-text">{{ index + 1 + ' ' + item.templateName?.replace('绩效奖计算表', '') }}</span>
            </div>
            <div class="dept-info">
              <span class="dept-label">科室：</span>
              <span class="dept-value">{{ item.templateDeptName }}</span>
            </div>
            <div class="dept-progress">
              <div class="calc-status">
                <span v-if="Number(getCalcProgress(item.id)[0]) != 0" class="calc-status-text">
                  {{ getCalcProgress(item.id)[1] }}
                </span>
                <n-tag v-else type="info" size="small" class="status-tag">未计算</n-tag>
              </div>
              <n-tooltip
                v-if="Object.keys(getErrorsFunction(item.id)).length != 0"
                trigger="hover"
                style="background-color: transparent; border: none; padding: 0"
                :show-arrow="false"
              >
                <template #trigger>
                  <n-tag type="warning" size="small" class="error-tag">
                    {{ Object.keys(getErrorsFunction(item.id)).length }}条存在问题
                  </n-tag>
                </template>
                <ErrorCard :error="getErrorsFunction(item.id)" />
              </n-tooltip>
            </div>
          </div>
        </n-list-item>
      </n-list>
    </n-scrollbar>
  </div>
</template>


<script setup lang="ts">
  import ErrorCard from './ErrorCard.vue'
  import FormulaDisplay from './FormulaDisplay.vue'
  import { SearchOutline, BuildOutline } from '@vicons/ionicons5'
const props = defineProps<{
  getCalcProgress: (templateId: string) => string[]
  getErrorsFunction: (id: string) => Record<string, any>
  treeHeight: string
  filteredTemplateList: any[]
  selectedDeptTemplate: any
  handleDeptClick: (item: any) => void
}>()

const deptSearchPattern = defineModel<string>('deptSearchPattern', { required: true })




</script>

<style scoped lang="scss">

// 左侧科室列表
.dept-list-card {
  width: 220px; // 减小宽度
  height: 100%;
  border-radius: 3px;
  border: 1px solid #e5e7eb;
  background-color: #fff;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  padding: 0;
}

.dept-header {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-bottom: 1px solid #f3f4f6;
  background-color: #f9fafb;
  gap: 8px;
  font-weight: 500;
  font-size: 14px;
  color: #1f2937;
}

.dept-icon {
  color: #0960bd;
}

.dept-search {
  width:90%;
  margin: 3%;
  // padding: 8px; 
  
  :deep(.n-input) {
    font-size: 13px;
    
    .n-input__border,
    .n-input__state-border {
      border-radius: 3px;
    }
  }
}

.dept-scrollbar {
  flex: 1;
  
  :deep(.n-scrollbar-container) {
    border-radius: 0;
  }
}

.dept-item {
  padding: 8px 12px;
  border-bottom: 1px solid #f3f4f6;
  
  &:hover {
    background-color: #f9fafb;
  }
}

.dept-name {
  font-size: 13px;
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 4px;
}

.dept-name-text {
  font-weight: 500;
  color: #1f2937;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.dept-type-icon {
  color: #0960bd;
  flex-shrink: 0;
}

.dept-info {
  display: flex;
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 4px;
}

.dept-value {
  color: #374151;
  font-weight: 500;
}

.dept-type-buttons {
  display: flex;
  gap: 4px;
  margin-bottom: 4px;
  flex-wrap: wrap;
}
.dept-progress {
  margin-top: 4px;
}

.calc-status {
  display: flex;
  align-items: center;
}

.calc-status-text {
  color: #6b7280;
}

</style>