<template>
  <div class="mobile-approval-page">
    <!-- 顶部Tab切换 -->
    <div class="approval-tabs-container">
      <n-tabs v-model:value="activeTab" type="segment" animated size="medium" class="approval-tabs">
        <n-tab-pane name="todo" :tab="renderTabWithBadge('todo', '待办', todoCount, 'error')" />
        <n-tab-pane name="done" :tab="renderTabWithBadge('done', '已办', doneCount, 'success')" />
        <n-tab-pane name="my" :tab="renderTabWithBadge('my', '我的流程', myCount, 'warning')" />
        <n-tab-pane name="copy" :tab="renderTabWithBadge('copy', '抄送', copyCount, 'info')" />
      </n-tabs>
    </div>

    <!-- 筛选操作栏 -->
    <div class="filter-bar">
      <div class="filter-actions">
        <n-input
          v-model:value="searchKeyword"
          placeholder="搜索科室/人员/任务..."
          clearable
          size="medium"
          class="search-input"
        >
          <template #prefix>
            <n-icon size="16">
              <SearchOutline />
            </n-icon>
          </template>
        </n-input>

        <n-select v-model:value="dateFilter" size="medium" :options="dateFilterOptions" class="date-filter" />
      </div>
    </div>

    <!-- 任务列表 - 固定高度可滚动 -->
    <PullToRefresh
      ref="pullToRefreshRef"
      :refreshing-text="refreshingText"
      @refresh="handlePullRefresh"
      class="task-list-container"
    >
      <!-- 待办任务 -->
      <div v-if="activeTab === 'todo'" class="task-section">
        <!-- <div class="section-header">
          <n-icon size="18" color="#409eff"><ClockOutline /></n-icon>
          <span>待办任务</span>
        </div> -->

        <div v-if="loading" class="loading-container">
          <n-spin size="medium" />
        </div>

        <div v-else-if="filteredTodoList.length === 0" class="empty-container">
          <n-empty description="暂无待办任务" />
        </div>

        <div v-else class="task-list" :style="{ height: taskListHeight + 'px', overflowY: 'auto' }">
          <div
            v-for="(item, index) in filteredTodoList"
            :key="'todo-' + index"
            :data-task-index="index"
            class="task-item"
            @click="
              () => {
                handleTaskClick(item)

                handleViewHistory(item)
              }
            "
          >
            <div class="task-header">
              <div class="task-title">{{ item.processInstance?.name }}</div>
              <div class="task-status pending">待处理</div>
              <!-- <div class="task-actions"> -->
              <!-- </div> -->
            </div>

            <div class="task-desc">
              <n-icon size="14"><SettingsOutline /></n-icon>
              当前任务：{{ item.name }}
            </div>

            <div class="task-info">
              <div class="info-item">
                <n-icon size="12"><PersonOutline /></n-icon>
                <span>{{ item.processInstance?.startUser?.empName || '未知用户' }}</span>
              </div>
              <div class="info-item">
                <n-icon size="12"><BusinessOutline /></n-icon>
                <span>{{ item.processInstance?.startUser?.deptName || '未知科室' }}</span>
              </div>
              <div class="info-item">
                <n-icon size="12"><TimeOutline /></n-icon>
                <span>{{ formatDate(item.createTime) }}</span>
              </div>
            </div>
          </div>

          <!-- 加载更多指示器 - 待办 -->
          <div v-if="paginationState.todo.hasMore && paginationState.todo.loading" class="loading-more-indicator">
            <n-spin size="small" />
            <span class="text-sm text-gray-500 ml-2">加载更多...</span>
          </div>
        </div>
      </div>

      <!-- 已办任务 -->
      <div v-if="activeTab === 'done'" class="task-section">
        <!-- <div class="section-header">
          <n-icon size="18" color="#67c23a"><CheckmarkCircleOutline /></n-icon>
          <span>已办任务</span>
        </div> -->

        <div v-if="loading" class="loading-container">
          <n-spin size="medium" />
        </div>

        <div v-else-if="filteredDoneList.length === 0" class="empty-container">
          <n-empty description="暂无已办任务" />
        </div>

        <div v-else class="task-list" :style="{ height: taskListHeight + 'px', overflowY: 'auto' }">
          <div
            v-for="(item, index) in filteredDoneList"
            :key="'done-' + index"
            :data-task-index="index"
            class="task-item"
            @click="
              () => {
                handleTaskClick(item)

                handleViewHistory(item)
              }
            "
          >
            <div class="task-header">
              <div class="task-title">{{ item.processInstance?.name }}</div>
              <div class="task-status done">已处理</div>
            </div>

            <div class="task-desc">
              <n-icon size="14"><CheckmarkCircleOutline /></n-icon>
              审批任务：{{ item.name }}
            </div>

            <div class="task-info">
              <div class="info-item">
                <n-icon size="12"><PersonOutline /></n-icon>
                <span>{{ item.processInstance?.startUser?.empName || '未知用户' }}</span>
              </div>
              <div class="info-item">
                <n-icon size="12"><BusinessOutline /></n-icon>
                <span>{{ item.processInstance?.startUser?.deptName || '未知科室' }}</span>
              </div>
              <div class="info-item">
                <n-icon size="12"><TimeOutline /></n-icon>
                <span>{{ formatDate(item.createTime) }}</span>
              </div>
            </div>

            <!-- <div class="task-actions">
              <n-button type="info" size="small" @click.stop="handleViewHistory(item)"> 查看 </n-button>
            </div> -->
          </div>

          <!-- 加载更多指示器 - 已办 -->
          <div v-if="paginationState.done.hasMore && paginationState.done.loading" class="loading-more-indicator">
            <n-spin size="small" />
            <span class="text-sm text-gray-500 ml-2">加载更多...</span>
          </div>
        </div>
      </div>

      <!-- 我的流程 -->
      <div v-if="activeTab === 'my'" class="task-section">
        <!-- <div class="section-header">
          <n-icon size="18" color="#e6a23c"><DocumentTextOutline /></n-icon>
          <span>我的流程</span>
        </div> -->

        <div v-if="loading" class="loading-container">
          <n-spin size="medium" />
        </div>

        <div v-else-if="filteredMyList.length === 0" class="empty-container">
          <n-empty description="暂无流程" />
        </div>

        <div v-else class="task-list" :style="{ height: taskListHeight + 'px', overflowY: 'auto' }">
          <div
            v-for="(item, index) in filteredMyList"
            :key="'my-' + index"
            :data-task-index="index"
            class="task-item"
            @click="
              () => {
                handleTaskClick(item)

                handleViewHistory(item)
              }
            "
          >
            <div class="task-header">
              <div class="task-title">{{ item.name }}</div>
              <div class="task-status" :class="getStatusClass(item.status)">
                {{ getStatusText(item.status) }}
              </div>
            </div>

            <div class="task-desc">
              <n-icon size="14"><FolderOutline /></n-icon>
              流程分类：{{ item.categoryName }}
            </div>

            <div class="task-info">
              <div class="info-item">
                <n-icon size="12"><PersonOutline /></n-icon>
                <span>{{ item.startUser?.empName || '我' }}</span>
              </div>
              <div class="info-item">
                <n-icon size="12"><BusinessOutline /></n-icon>
                <span>{{ item.startUser?.deptName || '我的科室' }}</span>
              </div>
              <div class="info-item">
                <n-icon size="12"><TimeOutline /></n-icon>
                <span>{{ formatDate(item.startTime) }}</span>
              </div>
            </div>

            <!-- <div class="task-actions">
              <n-button type="primary" size="small" @click.stop="handleDetail(item)"> 详情 </n-button>
            </div> -->
          </div>

          <!-- 加载更多指示器 - 我的流程 -->
          <div v-if="paginationState.my.hasMore && paginationState.my.loading" class="loading-more-indicator">
            <n-spin size="small" />
            <span class="text-sm text-gray-500 ml-2">加载更多...</span>
          </div>
        </div>
      </div>

      <!-- 抄送我的 -->
      <div v-if="activeTab === 'copy'" class="task-section">
        <!-- <div class="section-header">
          <n-icon size="18" color="#909399"><CopyOutline /></n-icon>
          <span>抄送我的</span>
        </div> -->

        <div v-if="loading" class="loading-container">
          <n-spin size="medium" />
        </div>

        <div v-else-if="filteredCopyList.length === 0" class="empty-container">
          <n-empty description="暂无抄送" />
        </div>

        <div v-else class="task-list" :style="{ height: taskListHeight + 'px', overflowY: 'auto' }">
          <div
            v-for="(item, index) in filteredCopyList"
            :key="'copy-' + index"
            :data-task-index="index"
            class="task-item"
            @click="handleTaskClick(item)"
          >
            <div class="task-header">
              <div class="task-title">{{ item.processInstanceName }}</div>
              <div class="task-status copied">已抄送</div>
            </div>

            <div class="task-desc">
              <n-icon size="14"><CopyOutline /></n-icon>
              抄送任务：{{ item.taskName }}
            </div>

            <div class="task-info">
              <div class="info-item">
                <n-icon size="12"><PersonOutline /></n-icon>
                <span>{{ item.startUserName || '未知用户' }}</span>
              </div>
              <div class="info-item">
                <n-icon size="12"><BusinessOutline /></n-icon>
                <span>{{ item.startDeptName || '未知科室' }}</span>
              </div>
              <div class="info-item">
                <n-icon size="12"><TimeOutline /></n-icon>
                <span>{{ formatDate(item.createTime) }}</span>
              </div>
            </div>

            <div class="task-actions">
              <n-button type="info" size="small" @click.stop="handleCopyDetail(item)"> 详情 </n-button>
            </div>
          </div>

          <!-- 加载更多指示器 - 抄送我的 -->
          <div v-if="paginationState.copy.hasMore && paginationState.copy.loading" class="loading-more-indicator">
            <n-spin size="small" />
            <span class="text-sm text-gray-500 ml-2">加载更多...</span>
          </div>
        </div>
      </div>
    </PullToRefresh>

    <!-- 流程详情弹窗 -->
    <ProcessInstanceDetailModal
      :processInstanceId="currentProcessInstanceId"
      v-model:show="processDetailVisible"
      @close="handleModalClose"
    />
  </div>
</template>

<script setup lang="tsx">
  import { ref, reactive, onMounted, watch, computed, nextTick, onUnmounted } from 'vue'
  import { useRouter } from 'vue-router'
  import {
    TimeOutline as ClockOutline,
    CheckmarkCircleOutline,
    DocumentTextOutline,
    CopyOutline,
    SettingsOutline,
    PersonOutline,
    BusinessOutline,
    TimeOutline,
    FolderOutline,
    SearchOutline,
  } from '@vicons/ionicons5'
  import * as TaskApi from '@/api/bpm/task'
  import * as ProcessInstanceApi from '@/api/bpm/processInstance'
  import ProcessInstanceDetailModal from '@/views/modules/bpm/processInstance/detail/processDetailModal.vue'
  import PullToRefresh from '@/components/mobile/PullToRefresh.vue'
  import { ElBadge } from 'element-plus'

  const router = useRouter()

  // 响应式数据
  const activeTab = ref('todo')
  const loading = ref(false)
  const dateFilter = ref('all')
  const searchKeyword = ref('')

  // 高度计算相关
  const windowHeight = ref(window.innerHeight)
  const taskListHeight = ref(0)

  // 计算任务列表容器的高度
  const calculateTaskListHeight = () => {
    nextTick(() => {
      try {
        // 获取当前视口高度
        const viewportHeight = windowHeight.value

        // 动态获取实际元素高度
        const headerEl = document.querySelector('.mobile-header') as HTMLElement
        const breadcrumbEl = document.querySelector('.mobile-breadcrumb') as HTMLElement
        const tabsEl = document.querySelector('.approval-tabs-container') as HTMLElement
        const filterEl = document.querySelector('.filter-bar') as HTMLElement
        const bottomNavEl = document.querySelector('.mobile-bottom-nav') as HTMLElement

        // 计算实际高度
        const headerHeight = headerEl ? headerEl.offsetHeight : 64 // 默认64px
        const breadcrumbHeight = breadcrumbEl ? breadcrumbEl.offsetHeight : 0
        const tabsHeight = tabsEl ? tabsEl.offsetHeight : 60 // 默认60px
        const filterHeight = filterEl ? filterEl.offsetHeight : 64 // 默认64px
        const bottomNavHeight = bottomNavEl ? bottomNavEl.offsetHeight : 70 // 默认70px

        // 安全区域
        const safeAreaTop = parseInt(
          getComputedStyle(document.documentElement).getPropertyValue('--safe-area-inset-top') || '0'
        )
        const safeAreaBottom = parseInt(
          getComputedStyle(document.documentElement).getPropertyValue('--safe-area-inset-bottom') || '0'
        )

        // 计算总的固定高度
        const totalFixedHeight =
          headerHeight + breadcrumbHeight + tabsHeight + filterHeight + bottomNavHeight + safeAreaTop + safeAreaBottom

        // 计算可用高度，留出一些缓冲空间
        const availableHeight = viewportHeight - totalFixedHeight - 20 // 20px缓冲

        // 设置最小高度，避免过小
        taskListHeight.value = Math.max(availableHeight, 200)

        // console.log('高度计算:', {
        //   viewportHeight,
        //   headerHeight,
        //   breadcrumbHeight,
        //   tabsHeight,
        //   filterHeight,
        //   bottomNavHeight,
        //   safeAreaTop,
        //   safeAreaBottom,
        //   totalFixedHeight,
        //   availableHeight,
        //   finalHeight: taskListHeight.value
        // })
      } catch (error) {
        console.error('高度计算失败:', error)
        // 降级方案：使用固定高度
        taskListHeight.value = Math.max(windowHeight.value - 300, 200)
      }
    })
  }

  // 监听窗口大小变化
  const handleResize = () => {
    windowHeight.value = window.innerHeight
    calculateTaskListHeight()
  }

  // 获取当前Tab的分页状态
  const getCurrentPagination = () => {
    const tabKey = activeTab.value as keyof typeof paginationState
    return paginationState[tabKey]
  }

  // 重置分页状态
  const resetPagination = (tabKey: string) => {
    const pagination = paginationState[tabKey as keyof typeof paginationState]
    if (pagination) {
      pagination.pageNo = 1
      pagination.hasMore = true
      pagination.loading = false
      pagination.total = 0
    }
  }

  // 设置Intersection Observer监听倒数第三个任务项
  const setupIntersectionObserver = () => {
    if (intersectionObserver.value) {
      intersectionObserver.value.disconnect()
    }

    intersectionObserver.value = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const currentPagination = getCurrentPagination()
            // 检查是否需要加载更多
            if (currentPagination.hasMore && !currentPagination.loading && !loading.value) {
              loadMoreData()
            }
          }
        })
      },
      {
        root: null, // 使用视口作为根
        rootMargin: '100px', // 提前100px触发
        threshold: 0.1, // 10%可见时触发
      }
    )
  }

  // 观察倒数第三个任务项
  const observeThirdLastItem = () => {
    nextTick(() => {
      try {
        // 获取当前活跃Tab的任务列表
        const currentList = getCurrentTaskList()
        if (currentList.length >= 3) {
          // 找到倒数第三个任务项
          const thirdLastIndex = currentList.length - 3
          const taskItems = document.querySelectorAll(`[data-task-index="${thirdLastIndex}"]`)

          if (taskItems.length > 0 && intersectionObserver.value) {
            // 清除之前的观察
            intersectionObserver.value.disconnect()
            // 重新设置观察器
            setupIntersectionObserver()
            // 观察倒数第三个元素
            intersectionObserver.value.observe(taskItems[0])
          }
        }
      } catch (error) {
        console.error('设置Intersection Observer失败:', error)
      }
    })
  }

  // 获取当前Tab的任务列表
  const getCurrentTaskList = () => {
    switch (activeTab.value) {
      case 'todo':
        return filteredTodoList.value
      case 'done':
        return filteredDoneList.value
      case 'my':
        return filteredMyList.value
      case 'copy':
        return filteredCopyList.value
      default:
        return []
    }
  }

  // 加载更多数据
  const loadMoreData = async () => {
    const currentPagination = getCurrentPagination()

    if (!currentPagination.hasMore || currentPagination.loading) {
      return
    }

    currentPagination.loading = true
    currentPagination.pageNo += 1

    try {
      switch (activeTab.value) {
        case 'todo':
          await fetchTodoData(true) // true表示追加数据
          break
        case 'done':
          await fetchDoneData(true)
          break
        case 'my':
          await fetchMyData(true)
          break
        case 'copy':
          await fetchCopyData(true)
          break
      }

      // 重新观察倒数第三个元素
      setTimeout(observeThirdLastItem, 100)
    } catch (error) {
      console.error('加载更多数据失败:', error)
      // 回滚页码
      currentPagination.pageNo -= 1
    } finally {
      currentPagination.loading = false
    }
  }

  // 下拉刷新处理
  const handlePullRefresh = async () => {
    try {
      console.log('开始下拉刷新，当前Tab:', activeTab.value)

      // 重置当前Tab的分页状态
      const currentTabKey = activeTab.value as keyof typeof paginationState
      resetPagination(currentTabKey)

      // 清空当前Tab的数据
      switch (activeTab.value) {
        case 'todo':
          todoList.value = []
          break
        case 'done':
          doneList.value = []
          break
        case 'my':
          myList.value = []
          break
        case 'copy':
          copyList.value = []
          break
      }

      // 重新获取数据
      switch (activeTab.value) {
        case 'todo':
          await fetchTodoData(false) // false表示重置数据
          break
        case 'done':
          await fetchDoneData(false)
          break
        case 'my':
          await fetchMyData(false)
          break
        case 'copy':
          await fetchCopyData(false)
          break
      }

      // 刷新完成后重新观察倒数第三个元素
      setTimeout(observeThirdLastItem, 200)

      console.log('下拉刷新完成')
    } catch (error) {
      console.error('下拉刷新失败:', error)
      // 可以显示错误提示
      window.$message?.error('刷新失败，请重试')
    }
  }

  // 统计数据
  const todoCount = ref(0)
  const doneCount = ref(0)
  const myCount = ref(0)
  const copyCount = ref(0)

  // 列表数据
  const todoList = ref([])
  const doneList = ref([])
  const myList = ref([])
  const copyList = ref([])

  // 分页配置 - 为每个Tab维护独立的分页状态
  const paginationState = reactive({
    todo: { pageNo: 1, pageSize: 20, hasMore: true, loading: false, total: 0 },
    done: { pageNo: 1, pageSize: 20, hasMore: true, loading: false, total: 0 },
    my: { pageNo: 1, pageSize: 20, hasMore: true, loading: false, total: 0 },
    copy: { pageNo: 1, pageSize: 20, hasMore: true, loading: false, total: 0 },
  })

  // 兼容性保持
  const queryParams = reactive({
    pageNo: 1,
    pageSize: 20,
  })

  // Intersection Observer 相关
  const intersectionObserver = ref<IntersectionObserver | null>(null)
  const loadingMoreRef = ref<HTMLElement | null>(null)

  // 下拉刷新相关
  const pullToRefreshRef = ref()
  const refreshingText = computed(() => {
    switch (activeTab.value) {
      case 'todo':
        return '正在刷新待办任务...'
      case 'done':
        return '正在刷新已办任务...'
      case 'my':
        return '正在刷新我的流程...'
      case 'copy':
        return '正在刷新抄送任务...'
      default:
        return '正在刷新...'
    }
  })

  // 详情弹窗
  const currentProcessInstanceId = ref('')
  const processDetailVisible = ref(false)

  // 日期筛选选项
  const dateFilterOptions = [
    { label: '今天', value: 'today' },
    { label: '本周', value: 'week' },
    { label: '本月', value: 'month' },
    { label: '全部', value: 'all' },
  ]

  // 初始化数据
  onMounted(() => {
    fetchAllData()
    // 初始化高度计算
    calculateTaskListHeight()
    // 监听窗口大小变化
    window.addEventListener('resize', handleResize)
    // 监听设备方向变化
    window.addEventListener('orientationchange', () => {
      setTimeout(calculateTaskListHeight, 100)
    })
    // 初始化Intersection Observer
    setupIntersectionObserver()
  })

  // 清理事件监听器
  onUnmounted(() => {
    window.removeEventListener('resize', handleResize)
    window.removeEventListener('orientationchange', calculateTaskListHeight)
    // 清理Intersection Observer
    if (intersectionObserver.value) {
      intersectionObserver.value.disconnect()
      intersectionObserver.value = null
    }
  })

  // 监听日期筛选器变化
  watch(dateFilter, () => {
    // 重置所有分页状态
    Object.keys(paginationState).forEach(key => {
      resetPagination(key)
    })
    fetchAllData()
  })

  // 监听Tab切换
  watch(activeTab, () => {
    // Tab切换时重新观察倒数第三个元素
    setTimeout(observeThirdLastItem, 100)
  })

  // 监听搜索关键词变化
  watch(searchKeyword, () => {
    // 搜索时重新观察倒数第三个元素
    setTimeout(observeThirdLastItem, 100)
  })

  // 渲染带徽章的Tab标题 - TSX写法
  const renderTabWithBadge = (_key: string, title: string, count: number, type: any = 'info') => {
    return (
      <div class="flex items-center gap-2">
        <span>{title}</span>
        {count > 0 && <ElBadge max={9999} value={count} size="small" type={type} style={{ fontSize: '10px' }} />}
      </div>
    )
  }

  // 筛选后的列表数据
  const filteredTodoList = computed(() => {
    if (!searchKeyword.value) return todoList.value
    return todoList.value.filter((item: any) => {
      const keyword = searchKeyword.value.toLowerCase()
      const processName = item.processInstance?.name?.toLowerCase() || ''
      const taskName = item.name?.toLowerCase() || ''
      const empName = item.processInstance?.startUser?.empName?.toLowerCase() || ''
      const deptName = item.processInstance?.startUser?.deptName?.toLowerCase() || ''

      return (
        processName.includes(keyword) ||
        taskName.includes(keyword) ||
        empName.includes(keyword) ||
        deptName.includes(keyword)
      )
    })
  })

  const filteredDoneList = computed(() => {
    if (!searchKeyword.value) return doneList.value
    return doneList.value.filter((item: any) => {
      const keyword = searchKeyword.value.toLowerCase()
      const processName = item.processInstance?.name?.toLowerCase() || ''
      const taskName = item.name?.toLowerCase() || ''
      const empName = item.processInstance?.startUser?.empName?.toLowerCase() || ''
      const deptName = item.processInstance?.startUser?.deptName?.toLowerCase() || ''

      return (
        processName.includes(keyword) ||
        taskName.includes(keyword) ||
        empName.includes(keyword) ||
        deptName.includes(keyword)
      )
    })
  })

  const filteredMyList = computed(() => {
    if (!searchKeyword.value) return myList.value
    return myList.value.filter((item: any) => {
      const keyword = searchKeyword.value.toLowerCase()
      const processName = item.name?.toLowerCase() || ''
      const categoryName = item.categoryName?.toLowerCase() || ''
      const empName = item.startUser?.empName?.toLowerCase() || ''
      const deptName = item.startUser?.deptName?.toLowerCase() || ''

      return (
        processName.includes(keyword) ||
        categoryName.includes(keyword) ||
        empName.includes(keyword) ||
        deptName.includes(keyword)
      )
    })
  })

  const filteredCopyList = computed(() => {
    if (!searchKeyword.value) return copyList.value
    return copyList.value.filter((item: any) => {
      const keyword = searchKeyword.value.toLowerCase()
      const processName = item.processInstanceName?.toLowerCase() || ''
      const taskName = item.taskName?.toLowerCase() || ''
      const empName = item.startUserName?.toLowerCase() || ''
      const deptName = item.startDeptName?.toLowerCase() || ''

      return (
        processName.includes(keyword) ||
        taskName.includes(keyword) ||
        empName.includes(keyword) ||
        deptName.includes(keyword)
      )
    })
  })

  // 格式化日期
  const formatDate = (date: any) => {
    if (!date) return ''
    return new Date(date).toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  }

  // 获取所有模块数据
  const fetchAllData = async () => {
    loading.value = true
    try {
      await Promise.all([fetchTodoData(), fetchDoneData(), fetchMyData(), fetchCopyData()])
      // 数据加载完成后，观察倒数第三个元素
      setTimeout(observeThirdLastItem, 200)
    } finally {
      loading.value = false
    }
  }

  // 根据日期筛选获取日期范围
  const getDateRange = () => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())

    switch (dateFilter.value) {
      case 'today':
        return [today.toISOString().split('T')[0] + ' 00:00:00', now.toISOString().split('T')[0] + ' 23:59:59']
      case 'week':
        const firstDay = new Date(today)
        firstDay.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1))
        return [firstDay.toISOString().split('T')[0] + ' 00:00:00', now.toISOString().split('T')[0] + ' 23:59:59']
      case 'month':
        const firstDayOfMonth = new Date(now.getFullYear(), now.getMonth(), 1)
        return [
          firstDayOfMonth.toISOString().split('T')[0] + ' 00:00:00',
          now.toISOString().split('T')[0] + ' 23:59:59',
        ]
      default:
        return []
    }
  }

  // 获取待办任务数据
  const fetchTodoData = async (append = false) => {
    const pagination = paginationState.todo
    const params = {
      pageNo: append ? pagination.pageNo : 1,
      pageSize: pagination.pageSize,
    } as any

    if (dateFilter.value !== 'all') {
      const createTime = getDateRange()
      params.createTime = createTime.join(',')
    }

    try {
      const data = await TaskApi.getTaskTodoPage(params)
      const newList = data.list || []

      if (append) {
        // 追加模式：合并数据
        todoList.value = [...todoList.value, ...newList]
      } else {
        // 重置模式：替换数据
        todoList.value = newList
        pagination.pageNo = 1
      }

      // 更新分页状态
      pagination.total = data.total || 0
      pagination.hasMore = todoList.value.length < pagination.total
      todoCount.value = pagination.total

      console.log('待办任务分页状态:', {
        currentPage: pagination.pageNo,
        totalItems: pagination.total,
        currentItems: todoList.value.length,
        hasMore: pagination.hasMore,
        newItems: newList.length,
      })
    } catch (error) {
      console.error('获取待办任务失败:', error)
      if (!append) {
        todoList.value = []
        todoCount.value = 0
        pagination.total = 0
        pagination.hasMore = false
      }
    }
  }

  // 获取已办任务数据
  const fetchDoneData = async (append = false) => {
    const pagination = paginationState.done
    const params = {
      pageNo: append ? pagination.pageNo : 1,
      pageSize: pagination.pageSize,
    } as any

    if (dateFilter.value !== 'all') {
      params.createTime = getDateRange()
    }

    try {
      const data = await TaskApi.getTaskDonePage(params)
      const newList = data.list || []

      if (append) {
        doneList.value = [...doneList.value, ...newList]
      } else {
        doneList.value = newList
        pagination.pageNo = 1
      }

      pagination.total = data.total || 0
      pagination.hasMore = doneList.value.length < pagination.total
      doneCount.value = pagination.total
    } catch (error) {
      console.error('获取已办任务失败:', error)
      if (!append) {
        doneList.value = []
        doneCount.value = 0
        pagination.total = 0
        pagination.hasMore = false
      }
    }
  }

  // 获取我的流程数据
  const fetchMyData = async (append = false) => {
    const pagination = paginationState.my
    const params = {
      pageNo: append ? pagination.pageNo : 1,
      pageSize: pagination.pageSize,
    } as any

    if (dateFilter.value !== 'all') {
      params.createTime = getDateRange()
    }

    try {
      const data = await ProcessInstanceApi.getProcessInstanceMyPage(params)
      const newList = data.list || []

      if (append) {
        myList.value = [...myList.value, ...newList]
      } else {
        myList.value = newList
        pagination.pageNo = 1
      }

      pagination.total = data.total || 0
      pagination.hasMore = myList.value.length < pagination.total
      myCount.value = pagination.total
    } catch (error) {
      console.error('获取我的流程失败:', error)
      if (!append) {
        myList.value = []
        myCount.value = 0
        pagination.total = 0
        pagination.hasMore = false
      }
    }
  }

  // 获取抄送我的数据
  const fetchCopyData = async (append = false) => {
    const pagination = paginationState.copy
    const params = {
      pageNo: append ? pagination.pageNo : 1,
      pageSize: pagination.pageSize,
    } as any

    if (dateFilter.value !== 'all') {
      params.createTime = getDateRange()
    }

    try {
      const data = await ProcessInstanceApi.getProcessInstanceCopyPage(params)
      const newList = data.list || []

      if (append) {
        copyList.value = [...copyList.value, ...newList]
      } else {
        copyList.value = newList
        pagination.pageNo = 1
      }

      pagination.total = data.total || 0
      pagination.hasMore = copyList.value.length < pagination.total
      copyCount.value = pagination.total
    } catch (error) {
      console.error('获取抄送数据失败:', error)
      if (!append) {
        copyList.value = []
        copyCount.value = 0
        pagination.total = 0
        pagination.hasMore = false
      }
    }
  }

  // 处理任务点击
  const handleTaskClick = (item: any) => {
    // 可以在这里添加任务点击的通用逻辑
    console.log('任务点击:', item)
  }

  // 办理待办任务
  const handleAudit = (row: any) => {
    currentProcessInstanceId.value = row.processInstance.id
    processDetailVisible.value = true
  }

  // 查看历史任务
  const handleViewHistory = (row: any) => {
    router.push({
      path: '/bpm/processInstance/detail/index',
      query: {
        id: row.processInstance.id,
      },
    })
  }

  // 查看流程详情
  const handleDetail = (row: any) => {
    currentProcessInstanceId.value = row.id
    processDetailVisible.value = true
  }

  // 查看抄送任务详情
  const handleCopyDetail = (row: any) => {
    router.push({
      path: '/bpm/processInstance/detail/index',
      query: {
        id: row.processInstanceId,
      },
    })
  }

  // 弹窗关闭后刷新数据
  const handleModalClose = () => {
    fetchAllData()
  }

  // 获取状态样式类
  const getStatusClass = (status: number) => {
    switch (status) {
      case 1:
        return 'running'
      case 2:
        return 'approved'
      case 3:
        return 'rejected'
      case 4:
        return 'cancelled'
      default:
        return 'created'
    }
  }

  // 获取状态文本
  const getStatusText = (status: number) => {
    switch (status) {
      case 0:
        return '创建'
      case 1:
        return '审批中'
      case 2:
        return '审批通过'
      case 3:
        return '审批不通过'
      case 4:
        return '已取消'
      default:
        return '未知'
    }
  }
</script>

<style scoped>
  @reference "tailwindcss";

  .mobile-approval-page {
    /* margin-top: -50px; */
    @apply bg-gray-50 flex flex-col overflow-y-hidden;
  }

  /* Tab容器样式 */
  .approval-tabs-container {
    @apply bg-white border-b border-gray-100 flex-shrink-0;
  }

  .approval-tabs {
    @apply px-1;
  }

  .approval-tabs :deep(.n-tabs-nav) {
    @apply border-b-0;
  }

  .approval-tabs :deep(.n-tabs-tab) {
    @apply px-3 py-3;
  }

  .approval-tabs :deep(.n-tabs-tab-label) {
    @apply text-sm font-medium;
  }

  /* 统计卡片容器 */
  .stat-cards-container {
    @apply grid grid-cols-2 gap-3 p-4 bg-white;
  }

  .stat-card {
    @apply bg-white rounded-lg border border-gray-200 p-4 flex items-center gap-3 cursor-pointer transition-all duration-200 hover:shadow-sm active:bg-gray-50;
  }

  .card-icon {
    @apply flex items-center justify-center w-10 h-10 rounded-full bg-gray-50;
  }

  .card-content {
    @apply flex-1;
  }

  .card-value {
    @apply text-xl font-bold text-gray-900 leading-none;
  }

  .card-label {
    @apply text-xs text-gray-500 mt-1;
  }

  /* 筛选操作栏 */
  .filter-bar {
    @apply p-4 bg-white border-t border-gray-100 flex-shrink-0;
  }

  .filter-actions {
    @apply flex items-center  gap-2;
  }

  .date-filter {
    @apply w-20;
  }

  .search-input {
    @apply flex-1 ml-2;
  }

  /* 任务列表容器 */
  .task-list-container {
    @apply flex-1 bg-white;
    /* 移除 overflow-y-auto，由 PullToRefresh 组件处理滚动 */
  }

  .task-section {
    @apply p-4;
  }

  .section-header {
    @apply flex items-center gap-2 pb-3 border-b border-gray-100 mb-4;
  }

  .section-header span {
    @apply text-base font-medium text-gray-900;
  }

  /* 加载和空状态 */
  .loading-container {
    @apply flex justify-center py-8;
  }

  .empty-container {
    @apply py-8;
  }

  /* 任务列表 */
  .task-list {
    @apply space-y-3;

    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }

  .task-item {
    @apply bg-white rounded-lg border border-gray-200 p-4 cursor-pointer transition-all duration-200 hover:shadow-sm active:bg-gray-50;
  }

  .task-header {
    @apply flex items-start justify-between mb-3;
  }

  .task-title {
    @apply text-base font-medium text-gray-900 flex-1 pr-2 leading-tight;
  }

  .task-status {
    @apply px-2 py-1 rounded-full text-xs font-medium whitespace-nowrap;
  }

  .task-status.pending {
    @apply bg-orange-100 text-orange-700;
  }

  .task-status.done {
    @apply bg-green-100 text-green-700;
  }

  .task-status.running {
    @apply bg-blue-100 text-blue-700;
  }

  .task-status.approved {
    @apply bg-green-100 text-green-700;
  }

  .task-status.rejected {
    @apply bg-red-100 text-red-700;
  }

  .task-status.cancelled {
    @apply bg-gray-100 text-gray-700;
  }

  .task-status.created {
    @apply bg-blue-100 text-blue-700;
  }

  .task-status.copied {
    @apply bg-gray-100 text-gray-700;
  }

  .task-desc {
    @apply flex items-center gap-2 text-sm text-gray-600 mb-3;
  }

  .task-info {
    @apply flex flex-wrap gap-x-4 gap-y-1 mb-3;
  }

  .info-item {
    @apply flex items-center gap-1 text-xs text-gray-500;
  }

  .task-actions {
    @apply flex justify-end;
  }

  /* 响应式优化 */
  @media (max-width: 480px) {
    .stat-cards-container {
      @apply grid-cols-2 gap-2 p-3;
    }

    .stat-card {
      @apply p-3;
    }

    .card-value {
      @apply text-lg;
    }

    .card-label {
      @apply text-xs;
    }

    .filter-bar {
      @apply p-3;
    }

    .task-section {
      @apply p-3;
    }

    .task-item {
      @apply p-3;
    }

    .task-title {
      @apply text-sm;
    }

    .task-desc {
      @apply text-xs;
    }

    .info-item {
      @apply text-xs;
    }
  }

  /* 触摸反馈 - 更加微妙的效果 */
  .stat-card:active,
  .task-item:active {
    @apply opacity-80;
  }

  /* 加载更多指示器 */
  .loading-more-indicator {
    @apply flex items-center justify-center py-4 text-gray-500;
  }

  /* 自定义滚动条 */
  .task-list-container {
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 transparent;
  }

  .task-list-container::-webkit-scrollbar {
    width: 4px;
  }

  .task-list-container::-webkit-scrollbar-track {
    background: transparent;
  }

  .task-list-container::-webkit-scrollbar-thumb {
    background-color: #cbd5e0;
    border-radius: 2px;
  }
</style>
