import { defineStore } from 'pinia'
import { Arrive, queryOaTaskNum, queryUserOrgTaskList } from '@/api/oa/oaTask/fastArriveWeb'
import { Ref, ref, watch } from 'vue'
import { useSysStore, useTempStore } from '@/store/index'

/**
 * 快速到达状态管理
 * @function useFastArriveStore
 * @returns {Object} 包含快速到达相关状态和方法的对象
 */
export const useFastArriveStore = defineStore('FastArrive', () => {
  // 系统状态存储
  const sysStore = useSysStore()
  // 临时存储
  const tempStore = useTempStore()

  const arriveList = ref<Array<Arrive>>([])

  const Refresh: Ref<Function> = ref(() => {
    console.log('RefreshInit')
  })

  /** @type {NodeJS.Timeout | null} 定时器引用 */
  let removeInterval = null

  /**
   * 刷新函数
   * @param {boolean} [immediate=false] 是否立即刷新
   */
  const RefreshValue = (immediate: boolean = false) => {
    let nowArriveList = []
    // 根据当前页面路径筛选到达列表
    if (!window.location.href.endsWith('#/gateway')) {
      nowArriveList = filterArriveListBySystem()
    } else {
      nowArriveList = arriveList.value
    }
    // 更新每个到达项的警告数量
    for (const item of nowArriveList) {
      if (item.queryNumUrl && item.queryNumUrl != '') {
        queryOaTaskNum(item).then(res => {
          const oldValue = Number(item.warnNum || 0)
          const newValue = Number(res?.data || 0)
          if (oldValue !== newValue) {
            // 使用Vue的响应式API进行更新
            const index = arriveList.value.findIndex(
              arriveItem => arriveItem.path === item.path && arriveItem.sysId === item.sysId
            )
            if (index !== -1) {
              // 通过数组方法触发响应式更新
              arriveList.value[index] = { ...arriveList.value[index], warnNum: newValue }
            }
          }
        })
      }
    }
  }

  /**
   * 根据当前系统ID过滤到达列表
   * @param {boolean} [needRule=false] - 是否需要路由规则过滤
   * @returns {Array<Arrive>} 过滤后的到达列表
   */
  const filterArriveListBySystem = (needRule: boolean = false): Array<Arrive> => {
    if (!needRule) {
      return arriveList.value.filter((item: any) => sysStore.getSystemInfo.systemId == item.sysId)
    }

    const routes = sysStore.routes.reduce((acc, route) => {
      acc.push(route)
      if (route.children) {
        acc.push(...route.children)
      }
      return acc
    }, [])

    return arriveList.value.filter((item: any) => {
      const hasRule = routes.some(route => route.path === item.path)
      return hasRule && sysStore.getSystemInfo.systemId === item.sysId
    })
  }

  /**
   * 获取快速到达数据
   * @async
   * @function getFastArrive
   */
  const getFastArrive = async () => {
    // 清除之前的定时器
    if (removeInterval) {
      clearInterval(removeInterval)
    }
    try {
      // 查询快速到达数据
      const res = await queryUserOrgTaskList({})

      console.log(res)

      // 如果没有记录，直接返回
      if (res.data.length == 0) {
        return
      }

      // 重置arriveList并保证响应式
      arriveList.value = res.data.map(item => ({ ...item }))

      // 根据环境变量判断是否禁用快速到达功能
      if (
        false &&
        import.meta.env.VITE_DISABLE_FAST_ARRIVE == 'true' &&
        import.meta.env.VITE_USER_NODE_ENV == 'development'
      ) {
        console.log('提示：小红点查询功能已禁用', {
          VITE_DISABLE_FAST_ARRIVE: import.meta.env.VITE_DISABLE_FAST_ARRIVE,
          VITE_USER_NODE_ENV: import.meta.env.VITE_USER_NODE_ENV,
        })
        return
      } else {
        Refresh.value = RefreshValue
      }
      // 设置定时刷新
      removeInterval = setInterval(() => {
        Refresh.value()
        // 强制更新UI
        forceRefreshUI()
      }, 1000 * 40)

      // 立即执行一次刷新
      Refresh.value(true)
    } catch (error) {
      console.error('An error occurred:', error)
    }
  }

  /**
   * 强制刷新UI，确保待办数量更新后UI能及时反映
   */
  const forceRefreshUI = () => {
    // 通过创建一个新数组触发响应式更新
    arriveList.value = [...arriveList.value]
  }

  /**
   * 增加当前页面的警告数量
   * @function nowPageWarnNumAdd
   * @param {number} warnNum - 要增加的警告数量
   */
  const nowPageWarnNumAdd = (warnNum: number) => {
    // 找到当前页面对应的到达项，并增加警告数量
    const currentItem = arriveList.value.find(item => window.location.hash.includes(item.path))
    if (currentItem) {
      currentItem.warnNum += warnNum
    }
  }

  /**
   * 获取当前路径的警告数量
   * @function nowPathWarnNum
   * @returns {number} 当前路径的警告数量
   */
  const nowPathWarnNum = () =>
    Math.max(arriveList.value.find(item => window.location.hash.includes(item.path))?.warnNum || 0, 0)

  // 监听历史系统信息变化
  watch(
    () => tempStore.getHisSystemInfo,
    (newVal: any) => {
      if (newVal) {
        console.log('FastArrive,watchSysInfo', newVal)
        Refresh.value()
      }
    }
  )

  // 返回状态和方法
  return {
    arriveList,
    getFastArrive,
    Refresh,
    nowPageWarnNumAdd,
    nowPathWarnNum,
    filterArriveListBySystem,
    forceRefreshUI,
  }
})
